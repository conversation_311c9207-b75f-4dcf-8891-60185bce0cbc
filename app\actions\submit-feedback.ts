"use server"

import { createClient } from "@supabase/supabase-js"

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY!

export async function submitFeedback(formData: {
  name: string
  email: string
  subject: string
  message: string
}) {
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey)

    // Insert feedback into Supabase
    const { error } = await supabase.from("feedback").insert([
      {
        name: formData.name,
        email: formData.email,
        subject: formData.subject,
        message: formData.message,
        created_at: new Date().toISOString(),
      },
    ])

    if (error) {
      console.error("Error submitting feedback:", error)
      return { success: false, error: "Failed to submit feedback. Please try again." }
    }

    return { success: true, message: "Thank you for your message. I'll get back to you soon." }
  } catch (error) {
    console.error("Error in submitFeedback:", error)
    return { success: false, error: "An unexpected error occurred. Please try again later." }
  }
}
