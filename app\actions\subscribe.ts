"use server"

import { createClient } from "@supabase/supabase-js"

// Initialize Supabase client with anon key (now that we have proper RLS policies)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error("Missing Supabase environment variables")
}

export async function subscribeToNewsletter(email: string) {
  if (!email || !email.includes("@")) {
    return { success: false, error: "Please provide a valid email address" }
  }

  try {
    // Create Supabase client with anon key (RLS policies will allow the insert)
    const supabase = createClient(supabaseUrl!, supabaseAnonKey!)

    // Check if email already exists
    const { data: existingSubscriber, error: queryError } = await supabase
      .from("subscribers")
      .select("email")
      .eq("email", email)
      .maybeSingle()

    if (queryError) {
      console.error("Error checking for existing subscriber:", queryError)
      return { success: false, error: "Failed to check subscription status. Please try again." }
    }

    if (existingSubscriber) {
      return { success: false, error: "You're already subscribed!" }
    }

    // Insert new subscriber
    const { error: insertError } = await supabase.from("subscribers").insert([
      {
        email,
        subscribed_at: new Date().toISOString(),
      },
    ])

    if (insertError) {
      console.error("Error inserting subscriber:", insertError)
      return { success: false, error: "Failed to subscribe. Please try again later." }
    }

    return { success: true, message: "Thank you for subscribing!" }
  } catch (error) {
    console.error("Error subscribing to newsletter:", error)
    return {
      success: false,
      error: "Failed to subscribe. Please try again later.",
    }
  }
}
