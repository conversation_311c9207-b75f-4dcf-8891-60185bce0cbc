@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern CSS Custom Properties */
:root {
  /* Enhanced Typography Scale */
  --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
  --font-size-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
  --font-size-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --font-size-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
  --font-size-6xl: clamp(3.75rem, 3rem + 3.75vw, 5rem);

  /* Enhanced Spacing Scale */
  --space-xs: clamp(0.25rem, 0.2rem + 0.25vw, 0.375rem);
  --space-sm: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem);
  --space-md: clamp(1rem, 0.8rem + 1vw, 1.5rem);
  --space-lg: clamp(1.5rem, 1.2rem + 1.5vw, 2.25rem);
  --space-xl: clamp(2rem, 1.6rem + 2vw, 3rem);
  --space-2xl: clamp(3rem, 2.4rem + 3vw, 4.5rem);
  --space-3xl: clamp(4rem, 3.2rem + 4vw, 6rem);

  /* Modern Shadow System */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Enhanced Border Radius */
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Modern Gradient System */
  --gradient-primary: linear-gradient(135deg, #ec4899 0%, #a855f7 50%, #3b82f6 100%);
  --gradient-secondary: linear-gradient(135deg, #f59e0b 0%, #ef4444 50%, #dc2626 100%);
  --gradient-accent: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
  --gradient-surface: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

body {
  margin: 0;
  font-family: 'Inter', sans-serif;
  background-color: var(--background);
  color: var(--foreground);
  scroll-behavior: smooth;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode specific styles */
.dark body {
  color-scheme: dark;
}

body {
  color-scheme: light;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Modern Typography Utilities */
  .text-fluid-xs { font-size: var(--font-size-xs); }
  .text-fluid-sm { font-size: var(--font-size-sm); }
  .text-fluid-base { font-size: var(--font-size-base); }
  .text-fluid-lg { font-size: var(--font-size-lg); }
  .text-fluid-xl { font-size: var(--font-size-xl); }
  .text-fluid-2xl { font-size: var(--font-size-2xl); }
  .text-fluid-3xl { font-size: var(--font-size-3xl); }
  .text-fluid-4xl { font-size: var(--font-size-4xl); }
  .text-fluid-5xl { font-size: var(--font-size-5xl); }
  .text-fluid-6xl { font-size: var(--font-size-6xl); }

  /* Modern Shadow Utilities */
  .shadow-modern-sm { box-shadow: var(--shadow-sm); }
  .shadow-modern-md { box-shadow: var(--shadow-md); }
  .shadow-modern-lg { box-shadow: var(--shadow-lg); }
  .shadow-modern-xl { box-shadow: var(--shadow-xl); }
  .shadow-modern-2xl { box-shadow: var(--shadow-2xl); }
  .shadow-modern-inner { box-shadow: var(--shadow-inner); }

  /* Gradient Utilities */
  .bg-gradient-primary { background: var(--gradient-primary); }
  .bg-gradient-secondary { background: var(--gradient-secondary); }
  .bg-gradient-accent { background: var(--gradient-accent); }
  .bg-gradient-surface { background: var(--gradient-surface); }
  .bg-gradient-dark { background: var(--gradient-dark); }

  /* 3D Effects and Advanced Animation Utilities */
  .transform-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .perspective-2000 {
    perspective: 2000px;
  }

  /* 3D Image Container Effects */
  .image-3d-container {
    position: relative;
    transform-style: preserve-3d;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  }

  .image-3d-container:hover {
    transform: rotateY(5deg) rotateX(5deg) scale(1.02);
  }

  .image-3d-shadow {
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 15px 25px rgba(0, 0, 0, 0.08),
      0 5px 10px rgba(0, 0, 0, 0.05);
  }

  .image-3d-shadow:hover {
    box-shadow:
      0 30px 60px rgba(0, 0, 0, 0.15),
      0 20px 35px rgba(0, 0, 0, 0.12),
      0 10px 15px rgba(0, 0, 0, 0.08);
  }

  /* Parallax Effects */
  .parallax-element {
    will-change: transform;
    transform: translate3d(0, 0, 0);
  }

  /* Staggered Animation Utilities */
  .stagger-children > * {
    opacity: 0;
    transform: translateY(20px);
    animation: stagger-fade-in 0.8s cubic-bezier(0.23, 1, 0.32, 1) forwards;
  }

  .stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
  .stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
  .stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
  .stagger-children > *:nth-child(4) { animation-delay: 0.4s; }
  .stagger-children > *:nth-child(5) { animation-delay: 0.5s; }

  @keyframes stagger-fade-in {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Enhanced Hover Effects */
  .hover-lift {
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  }

  .hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  /* Dark Mode Specific Utilities */
  .dark .bg-gradient-surface {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  .dark .image-3d-shadow {
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 15px 25px rgba(0, 0, 0, 0.2),
      0 5px 10px rgba(0, 0, 0, 0.15);
  }

  .dark .image-3d-shadow:hover {
    box-shadow:
      0 30px 60px rgba(0, 0, 0, 0.4),
      0 20px 35px rgba(0, 0, 0, 0.3),
      0 10px 15px rgba(0, 0, 0, 0.2);
  }

  /* Smooth theme transitions for all elements */
  * {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  }

  /* Enhanced dark mode text colors */
  .dark .text-gray-600 {
    color: rgb(156 163 175) !important;
  }

  .dark .text-gray-700 {
    color: rgb(209 213 219) !important;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Responsive Container */
.container {
  width: 100%;
  margin: 0 auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 320px) {
  .container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1400px;
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

/* Mobile-first responsive utilities */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

.safe-area-inset {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Responsive text scaling */
.text-responsive-sm {
  font-size: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
}

.text-responsive-base {
  font-size: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
}

.text-responsive-lg {
  font-size: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
}

.text-responsive-xl {
  font-size: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
}

.text-responsive-2xl {
  font-size: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
}

.text-responsive-3xl {
  font-size: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
}

.text-responsive-4xl {
  font-size: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
}

.text-responsive-5xl {
  font-size: clamp(3rem, 2.4rem + 3vw, 4rem);
}

.text-responsive-6xl {
  font-size: clamp(3.75rem, 3rem + 3.75vw, 5rem);
}

/* filepath: c:\icet\Portfolio-Pro-version\app\globals.css */
header {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 50;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

nav ul {
  display: flex;
  gap: 1.5rem;
}

nav a {
  text-decoration: none;
  font-weight: 500;
  color: #4a4a4a;
  transition: color 0.3s ease;
}

nav a:hover {
  color: #ec4899; /* Tailwind's pink-500 */
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Disable hover effects on touch devices */
  @media (hover: none) {
    .hover\:scale-105:hover {
      transform: none;
    }

    .hover\:shadow-lg:hover {
      box-shadow: none;
    }

    .group:hover .group-hover\:scale-105 {
      transform: none;
    }
  }

  /* Optimize animations for mobile */
  * {
    animation-duration: 0.3s !important;
    transition-duration: 0.3s !important;
  }

  /* Touch-friendly spacing */
  .mobile-touch-spacing > * + * {
    margin-top: 1rem;
  }

  /* Mobile typography adjustments */
  h1 {
    line-height: 1.2;
  }

  h2 {
    line-height: 1.3;
  }

  h3 {
    line-height: 1.4;
  }

  /* Mobile-friendly buttons */
  button, .btn {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
  }

  /* Mobile form improvements */
  input, textarea, select {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 44px;
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Tablet-specific optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .tablet-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
  /* Re-enable hover effects for desktop */
  .hover\:scale-105:hover {
    transform: scale(1.05);
  }

  .hover\:shadow-lg:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}
