'use client'

import { <PERSON><PERSON><PERSON>, ChevronDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import ProjectCard from "@/components/project-card"
import TestimonialCard from "@/components/testimonial-card"
import SkillCard from "@/components/skill-card"
import ContactForm from "@/components/contact-form"
import AnimatedCounter from "@/components/animated-counter"
import { ScrollReveal } from "@/components/scroll-reveal"
import { ParallaxSection } from "@/components/parallax-section"
import NewsletterForm from "@/components/newsletter-form"
import ThreeBackground from "@/components/three-background"
import { Enhanced3DImage } from "@/components/enhanced-3d-image"
import { StaggeredText } from "@/components/staggered-text"
import React, { useContext } from 'react'
import { DesignSystemContext } from '@/components/theme-provider'
import StickyHeader from '@/components/sticky-header'
import { ScrollIndicator } from '@/components/scroll-indicator'

export default function Home() {
  const { colors, typography } = useContext(DesignSystemContext)

  return (
    <main className="overflow-hidden">
      <ScrollIndicator />
      <StickyHeader />

      {/* Hero Section with Parallax Effect */}
      <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-pink-900/80 via-purple-900/80 to-pink-800/80 dark:from-pink-900/90 dark:via-purple-900/90 dark:to-pink-800/90">
        <div className="absolute inset-0 z-0">
          <ParallaxSection />
        </div>
        <div className="container mx-auto px-4 xs:px-2 sm:px-4 md:px-6 lg:px-8 py-16 xs:py-12 sm:py-16 md:py-20 lg:py-24 z-10 relative safe-area-inset">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 xs:gap-6 sm:gap-8 md:gap-10 lg:gap-12 xl:gap-16 items-center">
            <ScrollReveal direction="left" duration={0.6}>
              <div className="space-y-6 xs:space-y-4 sm:space-y-6 md:space-y-8 text-white text-center lg:text-left">
                <h1 className="text-fluid-4xl xs:text-fluid-3xl sm:text-fluid-4xl md:text-fluid-5xl lg:text-fluid-6xl font-bold leading-tight bg-gradient-to-r from-white via-pink-100 to-purple-100 bg-clip-text text-transparent">
                  Simple is better <br className="hidden sm:block" />
                  than complex.
                </h1>
                <p className="text-fluid-lg md:text-fluid-xl opacity-90 max-w-lg leading-relaxed">
                  I transform concepts into clean, functional, and user-focused digital experiences that elevate brands
                  and engage users.
                </p>
                <div className="flex flex-col sm:flex-row gap-6">
                  <Button size="lg" variant="gradient" className="group" onClick={()=>document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}>
                    View Projects <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                  <Button size="lg" variant="modern" onClick={()=>document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}>
                    Contact Me
                  </Button>
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.3} direction="right" duration={1.2}>
              <div className="relative h-[300px] xs:h-[250px] sm:h-[300px] md:h-[350px] lg:h-[400px] grid grid-cols-2 gap-2 xs:gap-1 sm:gap-2 md:gap-4">
                <div className="absolute top-0 right-0 w-32 h-32 xs:w-28 xs:h-28 sm:w-36 sm:h-36 md:w-48 md:h-48 lg:w-64 lg:h-64 rounded-xl xs:rounded-lg sm:rounded-xl md:rounded-2xl overflow-hidden shadow-modern-xl transform rotate-3 hover:rotate-0 transition-all duration-500 hover:scale-105" style={{ animationDelay: '2s' }}>
                  <img src="/download1.jpeg" alt="Portfolio image" className="w-full h-full object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
                </div>
                <div className="absolute bottom-0 left-0 w-32 h-32 xs:w-28 xs:h-28 sm:w-36 sm:h-36 md:w-48 md:h-48 lg:w-64 lg:h-64 rounded-xl xs:rounded-lg sm:rounded-xl md:rounded-2xl overflow-hidden shadow-modern-xl transform -rotate-3 hover:rotate-0 transition-all duration-500 hover:scale-105" style={{ animationDelay: '2s' }}>
                  <img src="/download.jpeg" alt="Portfolio image" className="w-full h-full object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
                </div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 xs:w-28 xs:h-28 sm:w-36 sm:h-36 md:w-48 md:h-48 lg:w-64 lg:h-64 rounded-xl xs:rounded-lg sm:rounded-xl md:rounded-2xl overflow-hidden shadow-modern-2xl hover:scale-110 transition-all duration-500 z-10" style={{ animationDelay: '4s' }}>
                  <img src="/download2.jpeg" alt="Portfolio image" className="w-full h-full object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
                </div>
              </div>
            </ScrollReveal>
          </div>

          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce-gentle">
            <div className="p-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20">
              <ChevronDown className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </section>

      {/* Rest of the page content remains the same */}
      {/* Services/Skills Section */}
      <section id="services" className="relative py-16 xs:py-12 sm:py-16 md:py-20 lg:py-24 bg-gradient-surface dark:bg-gradient-dark z-10 overflow-hidden">
        {/* Decorative Light Orbs */}
        <div className="absolute inset-0 pointer-events-none">
          {/* Top Left Orb */}
          <div className="absolute top-8 left-8 xs:top-4 xs:left-4 sm:top-6 sm:left-6 md:top-8 md:left-8 lg:top-12 lg:left-12">
            <div className="w-16 h-16 xs:w-12 xs:h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 lg:w-20 lg:h-20 xl:w-24 xl:h-24 rounded-full bg-gradient-to-br from-pink-200/30 via-purple-200/20 to-blue-200/10 dark:from-pink-400/20 dark:via-purple-400/15 dark:to-blue-400/10 blur-sm animate-pulse-slow opacity-60 dark:opacity-80"></div>
          </div>

          {/* Top Right Orb */}
          <div className="absolute top-8 right-8 xs:top-4 xs:right-4 sm:top-6 sm:right-6 md:top-8 md:right-8 lg:top-12 lg:right-12">
            <div className="w-20 h-20 xs:w-14 xs:h-14 sm:w-16 sm:h-16 md:w-20 md:h-20 lg:w-24 lg:h-24 xl:w-28 xl:h-28 rounded-full bg-gradient-to-bl from-purple-200/25 via-pink-200/15 to-indigo-200/10 dark:from-purple-400/18 dark:via-pink-400/12 dark:to-indigo-400/8 blur-md animate-float opacity-50 dark:opacity-70" style={{ animationDelay: '1s' }}></div>
          </div>

          {/* Bottom Left Orb */}
          <div className="absolute bottom-8 left-8 xs:bottom-4 xs:left-4 sm:bottom-6 sm:left-6 md:bottom-8 md:left-8 lg:bottom-12 lg:left-12">
            <div className="w-18 h-18 xs:w-10 xs:h-10 sm:w-12 sm:h-12 md:w-18 md:h-18 lg:w-22 lg:h-22 xl:w-26 xl:h-26 rounded-full bg-gradient-to-tr from-blue-200/20 via-purple-200/15 to-pink-200/10 dark:from-blue-400/15 dark:via-purple-400/12 dark:to-pink-400/8 blur-lg animate-pulse-slow opacity-40 dark:opacity-60" style={{ animationDelay: '2s' }}></div>
          </div>

          {/* Bottom Right Orb */}
          <div className="absolute bottom-8 right-8 xs:bottom-4 xs:right-4 sm:bottom-6 sm:right-6 md:bottom-8 md:right-8 lg:bottom-12 lg:right-12">
            <div className="w-22 h-22 xs:w-16 xs:h-16 sm:w-18 sm:h-18 md:w-22 md:h-22 lg:w-26 lg:h-26 xl:w-30 xl:h-30 rounded-full bg-gradient-to-tl from-indigo-200/30 via-purple-200/20 to-pink-200/15 dark:from-indigo-400/22 dark:via-purple-400/16 dark:to-pink-400/12 blur-xl animate-float opacity-45 dark:opacity-75" style={{ animationDelay: '3s' }}></div>
          </div>

          {/* Additional Floating Orbs for Enhanced Effect */}
          <div className="absolute top-1/4 left-1/4 xs:hidden sm:block">
            <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 rounded-full bg-gradient-to-r from-pink-300/15 to-purple-300/10 dark:from-pink-500/12 dark:to-purple-500/8 blur-md animate-pulse-slow opacity-30 dark:opacity-50" style={{ animationDelay: '4s' }}></div>
          </div>

          <div className="absolute top-3/4 right-1/3 xs:hidden md:block">
            <div className="w-6 h-6 md:w-8 md:h-8 lg:w-10 lg:h-10 rounded-full bg-gradient-to-l from-blue-300/20 to-indigo-300/15 dark:from-blue-500/15 dark:to-indigo-500/10 blur-sm animate-float opacity-25 dark:opacity-45" style={{ animationDelay: '5s' }}></div>
          </div>
        </div>

        <div className="container mx-auto px-4 xs:px-2 sm:px-4 md:px-6 lg:px-8 safe-area-inset relative z-10">
          <ScrollReveal direction="fade" duration={1.0}>
            <div className="text-center mb-12 xs:mb-8 sm:mb-12 md:mb-16 lg:mb-20">
              <h2 className="text-fluid-3xl xs:text-fluid-2xl sm:text-fluid-3xl md:text-fluid-4xl lg:text-fluid-5xl font-bold mb-4 xs:mb-3 sm:mb-4 md:mb-6 bg-gradient-to-r from-gray-900 via-purple-900 to-pink-900 dark:from-white dark:via-pink-200 dark:to-purple-200 bg-clip-text text-transparent">
              Transforming Your Brightest Ideas into Impactful Software Solutions.
              </h2>
              <p className="text-fluid-base xs:text-fluid-sm sm:text-fluid-base md:text-fluid-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed px-4 xs:px-2 sm:px-4">
                I specialize in creating digital solutions that not only look beautiful but also deliver exceptional user experiences and drive business results.
              </p>
            </div>
          </ScrollReveal>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 xs:gap-4 sm:gap-6 md:gap-8">
            <ScrollReveal delay={0.1}>
              <SkillCard
                title="Web Development"
                description="Custom websites that are clean, fast, and tailored to your needs."
                imageSrc="/webdev1.jpeg"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.2}>
              <SkillCard
                title="UI/UX Design"
                description="Designing intuitive interfaces and seamless user experiences that delight and engage."
                imageSrc="/images/ui-design.jpg"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.3}>
              <SkillCard
                title="Digital Strategy"
                description="Developing comprehensive digital strategies that align with business goals and user needs."
                imageSrc="/images/digital-strategy.jpg"
              />
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section id="stats" className="relative py-24 bg-white dark:bg-gray-900 z-10">
        <div className="container mx-auto px-4">
          <ScrollReveal direction="fade" duration={1.0}>
            <div className="text-center mb-20">
              <h2 className="text-fluid-3xl md:text-fluid-4xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-purple-900 to-pink-900 dark:from-white dark:via-pink-200 dark:to-purple-200 bg-clip-text text-transparent">
                Awareness and Presence in Everyday Moments
              </h2>
              <p className="text-fluid-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
                My work has made a significant impact across various industries and projects. Here are some numbers that
                showcase my experience and achievements.
              </p>
            </div>
          </ScrollReveal>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <ScrollReveal delay={0.1} direction="up" scale={0.9}>
              <div className="p-8 bg-white rounded-2xl shadow-modern-lg hover:shadow-modern-xl transition-all duration-300 hover:scale-105 border border-gray-100">
                <AnimatedCounter value={2} suffix="+" className="rounded-2xl text-fluid-4xl font-bold bg-gradient-primary bg-clip-text " />
                <p className="text-gray-600 mt-3 font-medium">Years Experience</p>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.2} direction="up" scale={0.9}>
              <div className="p-8 bg-white rounded-2xl shadow-modern-lg hover:shadow-modern-xl transition-all duration-300 hover:scale-105 border border-gray-100">
                <AnimatedCounter value={25} suffix="+" className="rounded-2xl text-fluid-4xl font-bold bg-gradient-primary bg-clip-text " />
                <p className="text-gray-600 mt-3 font-medium">Projects Completed</p>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.3} direction="up" scale={0.9}>
              <div className="p-8 bg-white rounded-2xl shadow-modern-lg hover:shadow-modern-xl transition-all duration-300 hover:scale-105 border border-gray-100">
                <AnimatedCounter value={12} suffix="+" className="rounded-2xl text-fluid-4xl font-bold bg-gradient-primary bg-clip-text " />
                <p className="text-gray-600 mt-3 font-medium">Happy Clients</p>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.4} direction="up" scale={0.9}>
              <div className="p-8 bg-white rounded-2xl shadow-modern-lg hover:shadow-modern-xl transition-all duration-300 hover:scale-105 border border-gray-100">
                <AnimatedCounter value={14} suffix="K+" className="rounded-2xl text-fluid-4xl font-bold bg-gradient-primary bg-clip-text " />
                <p className="text-gray-600 mt-3 font-medium">Lines of Code</p>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="relative py-24 bg-gradient-surface dark:bg-gradient-dark z-10">
        <div className="container mx-auto px-4">
          <ScrollReveal direction="fade" duration={1.0}>
            <div className="text-center mb-20">
              <h2 className="text-fluid-4xl md:text-fluid-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-purple-900 to-pink-900 dark:from-white dark:via-pink-200 dark:to-purple-200 bg-clip-text text-transparent">
                Featured Projects
              </h2>
              <p className="text-fluid-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Explore a selection of my recent work, showcasing innovative solutions and creative approaches to complex challenges.
              </p>
            </div>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <ScrollReveal delay={0.1}>
              <ProjectCard
                title="E-Commerce Platform"
                description="A modern e-commerce solution with seamless checkout and inventory management."
                imageSrc="/images/ecommerce-project.jpg"
                category="Web Development"
                date="January 2023"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.2}>
              <ProjectCard
                title="Financial Dashboard"
                description="Interactive dashboard for tracking investments and financial metrics."
                imageSrc="/images/finance-project.jpg"
                category="UI/UX Design"
                date="March 2023"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.3}>
              <ProjectCard
                title="Healthcare App"
                description="Mobile application for patient management and telehealth services."
                imageSrc="/images/healthcare-project.jpg"
                category="Mobile Development"
                date="June 2023"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.4}>
              <ProjectCard
                title="Social Media Analytics"
                description="Analytics platform for tracking social media performance and engagement."
                imageSrc="/images/social-project.jpg"
                category="Data Visualization"
                date="August 2023"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.5}>
              <ProjectCard
                title="Educational Platform"
                description="Online learning platform with interactive courses and progress tracking."
                imageSrc="/images/education-project.jpg"
                category="Web Development"
                date="October 2023"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.6}>
              <ProjectCard
                title="Travel Booking System"
                description="Comprehensive travel booking system with real-time availability and pricing."
                imageSrc="/images/travel-project.jpg"
                category="Full-Stack Development"
                date="December 2023"
              />
            </ScrollReveal>
          </div>

          <div className="text-center mt-16">
            <ScrollReveal delay={0.7} direction="fade">
              <Button size="lg" variant="gradient" className="group">
                View All Projects <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="relative py-24 bg-gradient-primary text-white z-10 overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-32 h-32 rounded-full bg-white/20 blur-2xl animate-pulse-slow"></div>
          <div className="absolute bottom-10 right-10 w-48 h-48 rounded-full bg-white/10 blur-3xl animate-float"></div>
          <div className="absolute top-1/2 left-1/3 w-24 h-24 rounded-full bg-white/15 blur-xl" style={{ animationDelay: '3s' }}></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <ScrollReveal direction="fade" duration={1.0}>
            <div className="text-center mb-20">
              <h2 className="text-fluid-4xl md:text-fluid-5xl font-bold mb-6 text-white">
                What they're saying about me
              </h2>
              <p className="text-fluid-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
                Don't just take my word for it. Here's what clients and collaborators have to say about working with me.
              </p>
            </div>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <ScrollReveal delay={0.1}>
              <TestimonialCard
                quote="Working with this developer was an absolute pleasure. They delivered our project on time and exceeded our expectations."
                author="Sarah Johnson"
                role="Marketing Director"
                avatarSrc="/images/avatar-1.jpg"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.2}>
              <TestimonialCard
                quote="The attention to detail and technical expertise brought to our project was impressive. We'll definitely work together again."
                author="Michael Chen"
                role="CEO, TechStart"
                avatarSrc="/images/avatar-2.jpg"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.3}>
              <TestimonialCard
                quote="Our website redesign transformed our online presence. The process was smooth and the results speak for themselves."
                author="Emily Rodriguez"
                role="Creative Director"
                avatarSrc="/images/avatar-3.jpg"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.4}>
              <TestimonialCard
                quote="Exceptional problem-solving skills and communication throughout the project. Highly recommended for complex web applications."
                author="David Wilson"
                role="Product Manager"
                avatarSrc="/images/avatar-4.jpg"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.5}>
              <TestimonialCard
                quote="The e-commerce platform developed for us has significantly increased our conversion rates and customer satisfaction."
                author="Jessica Lee"
                role="E-Commerce Director"
                avatarSrc="/images/avatar-5.jpg"
              />
            </ScrollReveal>

            <ScrollReveal delay={0.6}>
              <TestimonialCard
                quote="A true professional who delivers high-quality work consistently. Our app users love the intuitive interface design."
                author="Robert Taylor"
                role="CTO, MobileFirst"
                avatarSrc="/images/avatar-6.jpg"
              />
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Rest of the sections with z-index and semi-transparent backgrounds */}
      {/* About Me Section */}
      <section id="about" className="relative py-20 bg-white/90 dark:bg-gray-900/90 z-10 overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 xl:gap-16 items-center">
            {/* Enhanced 3D Image */}
              <img
                src="/img.png"
                alt="Portfolio owner - Professional developer"
                className="rounded-2xl"
              />

            {/* Enhanced Content with Staggered Animations */}
            <ScrollReveal
              delay={0.3}
              direction="right"
              duration={1.2}
              variant="stagger-children"
            >
              <div className="space-y-8">
                {/* Animated Title */}
                <ScrollReveal variant="text-reveal" delay={0.2}>
                  <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-pink-900 dark:from-white dark:via-pink-200 dark:to-purple-200 bg-clip-text text-transparent leading-tight">
                    Meet Your Developer
                  </h2>
                </ScrollReveal>

                {/* Staggered Paragraphs */}
                <div className="space-y-6">
                  <ScrollReveal delay={0.4} variant="parallax-float">
                    <StaggeredText
                      variant="words"
                      delay={0.6}
                      staggerDelay={0.02}
                      animation="fade-up"
                      className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed"
                    >
                      I'm a passionate web developer and designer with over 6 years of experience creating beautiful, functional websites and applications. My approach combines technical expertise with creative problem-solving to deliver solutions that exceed client expectations.
                    </StaggeredText>
                  </ScrollReveal>

                  <ScrollReveal delay={0.6} variant="parallax-float">
                    <StaggeredText
                      variant="words"
                      delay={0.8}
                      staggerDelay={0.02}
                      animation="fade-up"
                      className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed"
                    >
                      Specializing in modern web technologies like React, Next.js, and Tailwind CSS, I build responsive, accessible, and performant digital experiences that help businesses achieve their goals.
                    </StaggeredText>
                  </ScrollReveal>
                </div>

                {/* Enhanced Buttons with Hover Effects */}
                <ScrollReveal delay={1.2} variant="depth-lift">
                  <div className="flex flex-col sm:flex-row gap-4 pt-4">
                    <a
                      className="hover-lift group relative overflow-hidden inline-block px-6 py-3 text-lg font-medium text-white bg-gradient-to-r from-pink-600 to-purple-600 rounded-lg shadow hover:opacity-90 transition-opacity duration-300"
                      href="/resume.pdf"
                      target="_blank"
                      download="resume.pdf"
                    >
                      <span className="relative z-10">Download Resume</span>
                    </a>
                    <Button
                      size="lg"
                      variant="modern"
                      className="hover-lift backdrop-blur-sm border-2 border-white/20 hover:border-white/40 transition-all duration-300"
                    >
                      Contact Me
                    </Button>
                  </div>
                </ScrollReveal>

                {/* Floating Skill Indicators */}
                <ScrollReveal delay={1.4} variant="parallax-float">
                  <div className="flex flex-wrap gap-3 pt-6">
                    {['React', 'Next.js', 'TypeScript', 'Tailwind CSS'].map((skill, index) => (
                      <div
                        key={skill}
                        className="px-4 py-2 bg-gradient-to-r from-pink-100 to-purple-100 dark:from-pink-900/30 dark:to-purple-900/30 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300 hover-lift cursor-default"
                        style={{
                          animationDelay: `${1.6 + index * 0.1}s`
                        }}
                      >
                        {skill}
                      </div>
                    ))}
                  </div>
                </ScrollReveal>
              </div>
            </ScrollReveal>
          </div>
        </div>

        {/* Background Decorative Elements */}
        <div className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-br from-pink-400/10 to-purple-400/10 rounded-full blur-3xl animate-parallax-float" />
        <div className="absolute bottom-20 left-10 w-24 h-24 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-2xl animate-parallax-float" style={{ animationDelay: '2s' }} />
      </section>

      {/* FAQ Section */}
      <section className="relative py-20 bg-gray-50/90 dark:bg-gray-800/90 z-10">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 bg-gradient-to-r from-gray-900 via-purple-900 to-pink-900 dark:from-white dark:via-pink-200 dark:to-purple-200 bg-clip-text text-transparent">Frequently Asked Questions</h2>
          </ScrollReveal>

          <div className="max-w-3xl mx-auto space-y-6">
            <ScrollReveal delay={0.1}>
              <Card className="p-6 hover:shadow-lg transition-shadow duration-300">
                <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">What services do you offer?</h3>
                <p className="text-gray-700 dark:text-gray-300">
                  I offer a comprehensive range of web development services including frontend and backend development,
                  UI/UX design, responsive web design, e-commerce solutions, and custom web applications.
                </p>
              </Card>
            </ScrollReveal>

            <ScrollReveal delay={0.2}>
              <Card className="p-6 hover:shadow-lg transition-shadow duration-300">
                <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">What is your typical project timeline?</h3>
                <p className="text-gray-700 dark:text-gray-300">
                  Project timelines vary depending on complexity and scope. A simple website might take 2-4 weeks, while
                  more complex applications can take 2-3 months. I'll provide a detailed timeline during our initial
                  consultation.
                </p>
              </Card>
            </ScrollReveal>

            <ScrollReveal delay={0.3}>
              <Card className="p-6 hover:shadow-lg transition-shadow duration-300">
                <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Do you provide ongoing maintenance?</h3>
                <p className="text-gray-700 dark:text-gray-300">
                  Yes, I offer maintenance packages to ensure your website remains secure, up-to-date, and performing
                  optimally. These can be tailored to your specific needs and budget.
                </p>
              </Card>
            </ScrollReveal>

            <ScrollReveal delay={0.4}>
              <Card className="p-6 hover:shadow-lg transition-shadow duration-300">
                <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">How do we get started on a project?</h3>
                <p className="text-gray-700 dark:text-gray-300">
                  The process begins with an initial consultation to discuss your goals, requirements, and vision. From
                  there, I'll create a proposal outlining scope, timeline, and cost. Once approved, we'll move into the
                  design and development phases.
                </p>
              </Card>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="relative py-20 bg-gradient-to-br from-pink-900/90 via-purple-900/90 to-pink-800/90 dark:from-pink-900/95 dark:via-purple-900/95 dark:to-pink-800/95 text-white z-10">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <ScrollReveal>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Get notified about new projects and updates</h2>
              <p className="text-lg opacity-90 mb-8">
                Subscribe to my newsletter to receive updates on new projects, blog posts, and exclusive content.
              </p>

              <NewsletterForm />
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="relative py-20 bg-white/90 dark:bg-gray-900/90 z-10">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 bg-gradient-to-r from-gray-900 via-purple-900 to-pink-900 dark:from-white dark:via-pink-200 dark:to-purple-200 bg-clip-text text-transparent">Let's Work Together</h2>
          </ScrollReveal>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <ScrollReveal>
              <div className="space-y-6">
                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">Get in Touch</h3>
                <p className="text-lg text-gray-700 dark:text-gray-300">
                  Have a project in mind or want to discuss potential collaboration? Fill out the form or reach out
                  directly through the contact information below.
                </p>

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900/50 flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-pink-600 dark:text-pink-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-700 dark:text-gray-300"><EMAIL></span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900/50 flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-pink-600 dark:text-pink-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-700 dark:text-gray-300">+94 70 2268 958</span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900/50 flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-pink-600 dark:text-pink-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-700 dark:text-gray-300">Sri Lanka, Colombo</span>
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <a
                    href="#"
                    className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center hover:bg-pink-100 dark:hover:bg-pink-900/50 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-700 dark:text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                    </svg>
                  </a>
                  <a
                    href="#"
                    className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center hover:bg-pink-100 dark:hover:bg-pink-900/50 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-700 dark:text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                    </svg>
                  </a>
                  <a
                    href="#"
                    className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center hover:bg-pink-100 dark:hover:bg-pink-900/50 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-700 dark:text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z" />
                    </svg>
                  </a>
                  <a
                    href="#"
                    className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center hover:bg-pink-100 dark:hover:bg-pink-900/50 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-700 dark:text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.84.738 1.439.738 1.439.745 1.278 1.957.942 2.434.717.075-.54.283-.942.514-1.159-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                    </svg>
                  </a>
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.3}>
              <ContactForm />
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-10 bg-gray-900 dark:bg-gray-950 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <h3 className="text-xl font-bold">DevPortfolio</h3>
              <p className="text-gray-400">Creating beautiful digital experiences that make a difference.</p>
              <div className="flex gap-4">
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z" />
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.84.738 1.439.738 1.439.745 1.278 1.957.942 2.434.717.075-.54.283-.942.514-1.159-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                  </svg>
                </a>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-bold">Services</h3>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Web Development
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    UI/UX Design
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    E-Commerce
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Mobile Apps
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Digital Strategy
                  </a>
                </li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-bold">Resources</h3>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Case Studies
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Portfolio
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Testimonials
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    FAQ
                  </a>
                </li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-bold">Contact</h3>
              <ul className="space-y-2">
                <li className="text-gray-400"><EMAIL></li>
                <li className="text-gray-400">+94 70 2268 958</li>
                <li className="text-gray-400">Sri Lanka, Colombo</li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400">© 2023 Portfolio. All rights reserved.</p>
            <div className="flex gap-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                Terms of Service
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </footer>
    </main>
  )
}
