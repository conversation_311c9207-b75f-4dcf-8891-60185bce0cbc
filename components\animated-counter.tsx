"use client"

import { useEffect, useState, useRef } from "react"
import { useInView } from "react-intersection-observer"

interface AnimatedCounterProps {
  value: number
  suffix?: string
  className?: string
  duration?: number
}

export default function AnimatedCounter({ value, suffix = "", className = "", duration = 2000 }: AnimatedCounterProps) {
  const [count, setCount] = useState(0)
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })
  const countRef = useRef(0)
  const rafRef = useRef<number | null>(null)

  useEffect(() => {
    if (!inView) return

    const startTime = performance.now()
    const endValue = value

    const updateCount = (currentTime: number) => {
      const elapsedTime = currentTime - startTime
      const progress = Math.min(elapsedTime / duration, 1)
      const easedProgress = easeOutQuad(progress)
      const nextCount = Math.floor(easedProgress * endValue)

      if (countRef.current !== nextCount) {
        countRef.current = nextCount
        setCount(nextCount)
      }

      if (progress < 1) {
        rafRef.current = requestAnimationFrame(updateCount)
      } else {
        setCount(endValue)
      }
    }

    rafRef.current = requestAnimationFrame(updateCount)

    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current)
      }
    }
  }, [inView, value, duration])

  // Easing function for smoother animation
  const easeOutQuad = (t: number): number => t * (2 - t)

  return (
    <div ref={ref} className={className}>
      {count}
      {suffix}
    </div>
  )
}
