"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { submitFeedback } from "@/app/actions/submit-feedback"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export default function ContactForm() {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Submit to Supabase
      const result = await submitFeedback(formData)

      if (result.success) {
        toast({
          title: "Message sent!",
          description: result.message,
        })

        // Show success dialog
        setShowSuccessDialog(true)

        // Reset form
        setFormData({
          name: "",
          email: "",
          subject: "",
          message: "",
        })
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Form submission error:", error)
      toast({
        title: "Something went wrong",
        description: "Failed to send your message. Please try again later.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="p-4 xs:p-3 sm:p-4 md:p-5 lg:p-6">
      <form onSubmit={handleSubmit} className="space-y-4 xs:space-y-3 sm:space-y-4 md:space-y-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 xs:gap-3 sm:gap-4 md:gap-6">
          <div className="space-y-2 xs:space-y-1 sm:space-y-2">
            <label htmlFor="name" className="text-sm xs:text-xs sm:text-sm font-medium">
              Name
            </label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Your name"
              required
              className="w-full touch-target text-base xs:text-sm sm:text-base"
            />
          </div>
          <div className="space-y-2 xs:space-y-1 sm:space-y-2">
            <label htmlFor="email" className="text-sm xs:text-xs sm:text-sm font-medium">
              Email
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Your email"
              required
              className="w-full touch-target text-base xs:text-sm sm:text-base"
            />
          </div>
        </div>

        <div className="space-y-2 xs:space-y-1 sm:space-y-2">
          <label htmlFor="subject" className="text-sm xs:text-xs sm:text-sm font-medium">
            Subject
          </label>
          <Input
            id="subject"
            name="subject"
            value={formData.subject}
            onChange={handleChange}
            placeholder="Subject"
            required
            className="w-full touch-target text-base xs:text-sm sm:text-base"
          />
        </div>

        <div className="space-y-2 xs:space-y-1 sm:space-y-2">
          <label htmlFor="message" className="text-sm xs:text-xs sm:text-sm font-medium">
            Message
          </label>
          <Textarea
            id="message"
            name="message"
            value={formData.message}
            onChange={handleChange}
            placeholder="Your message"
            required
            className="w-full min-h-[120px] xs:min-h-[100px] sm:min-h-[120px] md:min-h-[150px] touch-target text-base xs:text-sm sm:text-base resize-none"
          />
        </div>

        <Button
          type="submit"
          className="w-full bg-pink-500 hover:bg-pink-600 text-white touch-target py-3 xs:py-2.5 sm:py-3 text-base xs:text-sm sm:text-base font-medium"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Sending..." : "Send Message"}
        </Button>
      </form>
      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl">Message Sent Successfully!</DialogTitle>
            <DialogDescription className="text-center">
              Thank you for reaching out. I'll get back to you as soon as possible.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-center py-4">
            <div className="rounded-full bg-green-100 p-3">
              <svg
                className="h-10 w-10 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
          <DialogFooter className="sm:justify-center">
            <Button
              type="button"
              className="bg-pink-500 hover:bg-pink-600 text-white"
              onClick={() => setShowSuccessDialog(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
