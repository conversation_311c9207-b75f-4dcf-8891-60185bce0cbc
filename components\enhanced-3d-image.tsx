'use client'

import React, { useRef, useEffect, useState } from 'react'
import { useInView } from 'react-intersection-observer'

interface Enhanced3DImageProps {
  src: string
  alt: string
  className?: string
  containerClassName?: string
  enableParallax?: boolean
  enableHover3D?: boolean
  aspectRatio?: 'square' | 'portrait' | 'landscape' | 'auto'
  priority?: boolean
}

export function Enhanced3DImage({
  src,
  alt,
  className = '',
  containerClassName = '',
  enableParallax = true,
  enableHover3D = true,
  aspectRatio = 'auto',
  priority = false
}: Enhanced3DImageProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isHovered, setIsHovered] = useState(false)
  
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
    rootMargin: '-50px 0px',
  })

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined'
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches
    : false

  // Handle mouse movement for 3D effect
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!enableHover3D || prefersReducedMotion) return
    
    const container = containerRef.current
    if (!container) return

    const rect = container.getBoundingClientRect()
    const x = (e.clientX - rect.left) / rect.width
    const y = (e.clientY - rect.top) / rect.height
    
    setMousePosition({ x, y })
  }

  const handleMouseEnter = () => {
    if (!prefersReducedMotion) {
      setIsHovered(true)
    }
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    setMousePosition({ x: 0.5, y: 0.5 })
  }

  // Parallax effect
  useEffect(() => {
    if (!enableParallax || prefersReducedMotion) return

    const handleScroll = () => {
      if (!containerRef.current) return
      
      const rect = containerRef.current.getBoundingClientRect()
      const scrolled = window.pageYOffset
      const rate = scrolled * -0.5
      
      if (rect.top <= window.innerHeight && rect.bottom >= 0) {
        containerRef.current.style.transform = `translate3d(0, ${rate}px, 0)`
      }
    }

    const rafId = requestAnimationFrame(function animate() {
      handleScroll()
      requestAnimationFrame(animate)
    })

    return () => cancelAnimationFrame(rafId)
  }, [enableParallax, prefersReducedMotion])

  // Calculate 3D transform based on mouse position
  const get3DTransform = () => {
    if (!enableHover3D || prefersReducedMotion || !isHovered) {
      return 'rotateY(0deg) rotateX(0deg) scale(1)'
    }

    const rotateY = (mousePosition.x - 0.5) * 20
    const rotateX = (mousePosition.y - 0.5) * -20
    const scale = 1.02

    return `rotateY(${rotateY}deg) rotateX(${rotateX}deg) scale(${scale})`
  }

  // Get aspect ratio classes
  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square':
        return 'aspect-square'
      case 'portrait':
        return 'aspect-[3/4]'
      case 'landscape':
        return 'aspect-[4/3]'
      default:
        return ''
    }
  }

  return (
    <div
      ref={ref}
      className={`relative perspective-1000 ${containerClassName}`}
    >
      <div
        ref={containerRef}
        className={`
          relative overflow-hidden rounded-2xl
          image-3d-container image-3d-shadow
          transform-3d backface-hidden
          transition-all duration-700 ease-out
          ${getAspectRatioClass()}
          ${inView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}
          ${isLoaded ? '' : 'bg-gray-200 dark:bg-gray-700 animate-pulse'}
        `}
        style={{
          transform: prefersReducedMotion ? 'none' : get3DTransform(),
          willChange: enableHover3D && !prefersReducedMotion ? 'transform' : 'auto',
          transitionDelay: inView ? '0.2s' : '0s'
        }}
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* Image */}
        <img
          ref={imageRef}
          src={src}
          alt={alt}
          className={`
            w-full h-full object-cover
            transition-all duration-700 ease-out
            ${aspectRatio === 'auto' ? 'object-contain' : 'object-cover'}
            ${className}
          `}
          style={{
            filter: isLoaded ? 'none' : 'blur(5px)',
          }}
          onLoad={() => setIsLoaded(true)}
          loading={priority ? 'eager' : 'lazy'}
        />

        {/* Overlay gradient for depth */}
        <div 
          className={`
            absolute inset-0 
            bg-gradient-to-t from-black/10 via-transparent to-transparent
            opacity-0 transition-opacity duration-500
            ${isHovered && !prefersReducedMotion ? 'opacity-100' : ''}
          `}
        />

        {/* Shine effect */}
        <div 
          className={`
            absolute inset-0 
            bg-gradient-to-r from-transparent via-white/20 to-transparent
            opacity-0 transition-all duration-700
            transform -skew-x-12 translate-x-[-100%]
            ${isHovered && !prefersReducedMotion ? 'opacity-100 translate-x-[100%]' : ''}
          `}
        />

        {/* Loading skeleton */}
        {!isLoaded && (
          <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded-2xl" />
        )}
      </div>

      {/* Floating elements for extra depth */}
      {enableHover3D && !prefersReducedMotion && (
        <>
          <div 
            className={`
              absolute -top-2 -right-2 w-4 h-4 
              bg-pink-400/30 rounded-full blur-sm
              transition-all duration-700
              ${isHovered ? 'scale-150 opacity-100' : 'scale-100 opacity-60'}
            `}
            style={{
              transform: `translate3d(${mousePosition.x * 10}px, ${mousePosition.y * 10}px, 0)`
            }}
          />
          <div 
            className={`
              absolute -bottom-2 -left-2 w-6 h-6 
              bg-purple-400/20 rounded-full blur-sm
              transition-all duration-700
              ${isHovered ? 'scale-125 opacity-100' : 'scale-100 opacity-40'}
            `}
            style={{
              transform: `translate3d(${mousePosition.x * -8}px, ${mousePosition.y * -8}px, 0)`
            }}
          />
        </>
      )}
    </div>
  )
}
