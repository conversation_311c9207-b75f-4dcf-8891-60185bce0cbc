"use client"

import { useEffect, useState, useRef } from "react"

export function ParallaxSection() {
  const [offset, setOffset] = useState(0)
  const rafRef = useRef<number>()

  useEffect(() => {
    const handleScroll = () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current)
      }

      rafRef.current = requestAnimationFrame(() => {
        setOffset(window.scrollY)
      })
    }

    // Use native scroll
    window.addEventListener("scroll", handleScroll, { passive: true })

    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current)
      }
      window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Enhanced gradient background with modern colors */}
      <div className="absolute inset-0 bg-gradient-primary opacity-90" />

      {/* Additional gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />

      {/* Enhanced floating elements with better performance */}
      <div
        className="absolute w-64 h-64 rounded-full bg-pink-400/30 blur-3xl will-change-transform"
        style={{
          top: "10%",
          left: "10%",
          transform: `translate3d(${offset * 0.05}px, ${offset * 0.02}px, 0)`,
        }}
      />
      <div
        className="absolute w-96 h-96 rounded-full bg-purple-400/25 blur-3xl will-change-transform"
        style={{
          top: "40%",
          right: "15%",
          transform: `translate3d(${-offset * 0.08}px, ${offset * 0.04}px, 0)`,
        }}
      />
      <div
        className="absolute w-80 h-80 rounded-full bg-blue-400/20 blur-3xl will-change-transform"
        style={{
          bottom: "10%",
          left: "30%",
          transform: `translate3d(${offset * 0.06}px, ${-offset * 0.03}px, 0)`,
        }}
      />

      {/* Additional floating elements for richness */}
      <div
        className="absolute w-48 h-48 rounded-full bg-yellow-400/15 blur-2xl will-change-transform"
        style={{
          top: "60%",
          right: "40%",
          transform: `translate3d(${offset * 0.03}px, ${offset * 0.05}px, 0)`,
        }}
      />
      <div
        className="absolute w-72 h-72 rounded-full bg-indigo-400/20 blur-3xl will-change-transform"
        style={{
          top: "20%",
          right: "5%",
          transform: `translate3d(${offset * 0.04}px, ${-offset * 0.02}px, 0)`,
        }}
      />

      {/* Enhanced grid pattern with modern styling */}
      <div
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage:
            "linear-gradient(to right, rgba(255,255,255,0.3) 1px, transparent 1px), linear-gradient(to bottom, rgba(255,255,255,0.3) 1px, transparent 1px)",
          backgroundSize: "60px 60px",
          transform: `translate3d(0, ${offset * 0.1}px, 0)`,
        }}
      />
    </div>
  )
}
