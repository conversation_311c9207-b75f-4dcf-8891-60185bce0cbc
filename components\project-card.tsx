import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Calendar } from "lucide-react"
import Link from "next/link"

interface ProjectCardProps {
  title: string
  description: string
  imageSrc: string
  category: string
  date: string
  link?: string
}

export default function ProjectCard({ title, description, imageSrc, category, date, link = "#" }: ProjectCardProps) {
  return (
    <Card className="overflow-hidden group h-full flex flex-col hover:shadow-lg transition-all duration-300 touch-target">
      <div className="relative h-40 xs:h-36 sm:h-40 md:h-44 lg:h-48 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10" />
        <div className="relative h-full w-full transform group-hover:scale-105 transition-transform duration-500">
          <img src={imageSrc || "/placeholder.svg"} alt={title} className="h-full w-full object-cover" />
        </div>
      </div>
      <CardContent className="flex-grow p-4 xs:p-3 sm:p-4 md:p-5">
        <div className="flex flex-col xs:flex-row justify-between items-start xs:items-center mb-2 xs:mb-1 sm:mb-2 gap-1 xs:gap-0">
          <span className="text-xs xs:text-xs sm:text-sm font-medium text-pink-600 dark:text-pink-400">{category}</span>
          <div className="flex items-center text-gray-500 dark:text-gray-400 text-xs xs:text-xs sm:text-sm">
            <Calendar className="h-3 w-3 xs:h-2.5 xs:w-2.5 sm:h-3 sm:w-3 mr-1" />
            <span>{date}</span>
          </div>
        </div>
        <h3 className="text-lg xs:text-base sm:text-lg md:text-xl font-bold mb-2 xs:mb-1 sm:mb-2 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors line-clamp-2">{title}</h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm xs:text-xs sm:text-sm line-clamp-3">{description}</p>
      </CardContent>
      <CardFooter className="p-4 xs:p-3 sm:p-4 md:p-5 pt-0">
        <Link
          href={link}
          className="text-pink-600 dark:text-pink-400 font-medium text-sm xs:text-xs sm:text-sm flex items-center hover:text-pink-700 dark:hover:text-pink-300 transition-colors touch-target py-2 px-1"
        >
          View Project
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 xs:h-3 xs:w-3 sm:h-4 sm:w-4 ml-1 transform group-hover:translate-x-1 transition-transform"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </Link>
      </CardFooter>
    </Card>
  )
}
