"use client"

import type React from "react"
import { useEffect, useState, useRef } from "react"
import { useInView } from "react-intersection-observer"

interface ScrollRevealProps {
  children: React.ReactNode
  delay?: number
  duration?: number
  distance?: string
  direction?: 'up' | 'down' | 'left' | 'right' | 'fade'
  reset?: boolean
  scale?: number
  rotate?: number
  blur?: number
  stagger?: boolean
  className?: string
  variant?: 'default' | 'text-reveal' | 'parallax-float' | 'depth-lift' | 'stagger-children'
  enableParallax?: boolean
}

export function ScrollReveal({
  children,
  delay = 0,
  duration = 0.8,
  distance = "60px",
  direction = 'up',
  reset = false,
  scale = 0.95,
  rotate = 0,
  blur = 0,
  stagger = false,
  className = "",
  variant = 'default',
  enableParallax = false,
}: ScrollRevealProps) {
  const [ref, inView] = useInView({
    triggerOnce: !reset,
    threshold: 0.1,
    rootMargin: '-50px 0px',
  })
  const [isVisible, setIsVisible] = useState(false)
  const elementRef = useRef<HTMLDivElement>(null)
  const [parallaxOffset, setParallaxOffset] = useState(0)

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined'
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches
    : false

  useEffect(() => {
    if (inView) {
      const timer = setTimeout(() => {
        setIsVisible(true)
      }, delay * 1000)
      return () => clearTimeout(timer)
    } else if (reset) {
      setIsVisible(false)
    }
  }, [inView, delay, reset])

  // Parallax effect
  useEffect(() => {
    if (!enableParallax || prefersReducedMotion) return

    const handleScroll = () => {
      if (!elementRef.current) return

      const rect = elementRef.current.getBoundingClientRect()
      const scrolled = window.pageYOffset
      const rate = scrolled * -0.3

      if (rect.top <= window.innerHeight && rect.bottom >= 0) {
        setParallaxOffset(rate)
      }
    }

    const rafId = requestAnimationFrame(function animate() {
      handleScroll()
      requestAnimationFrame(animate)
    })

    return () => cancelAnimationFrame(rafId)
  }, [enableParallax, prefersReducedMotion])

  // Get enhanced transform based on variant and direction
  const getTransform = (visible: boolean) => {
    if (prefersReducedMotion) return 'none'

    const transforms = []

    // Add parallax offset if enabled
    if (enableParallax) {
      transforms.push(`translate3d(0, ${parallaxOffset}px, 0)`)
    }

    if (!visible) {
      // Enhanced variants
      switch (variant) {
        case 'text-reveal':
          transforms.push(`translateY(30px) rotateX(90deg)`)
          break
        case 'depth-lift':
          transforms.push(`translateY(${distance}) translateZ(-50px)`)
          break
        case 'parallax-float':
          transforms.push(`translateY(${distance}) scale(${scale})`)
          break
        default:
          // Standard direction-based transforms
          switch (direction) {
            case 'up':
              transforms.push(`translateY(${distance})`)
              break
            case 'down':
              transforms.push(`translateY(-${distance})`)
              break
            case 'left':
              transforms.push(`translateX(${distance})`)
              break
            case 'right':
              transforms.push(`translateX(-${distance})`)
              break
            case 'fade':
              break
          }

          if (scale !== 1) transforms.push(`scale(${scale})`)
          if (rotate !== 0) transforms.push(`rotate(${rotate}deg)`)
      }
    }

    return transforms.length > 0 ? transforms.join(' ') : 'none'
  }

  const getFilter = (visible: boolean) => {
    if (prefersReducedMotion || blur === 0) return 'none'
    return visible ? 'none' : `blur(${blur}px)`
  }

  // Get variant-specific classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'stagger-children':
        return 'stagger-children'
      case 'text-reveal':
        return 'transform-3d'
      case 'depth-lift':
        return 'transform-3d perspective-1000'
      case 'parallax-float':
        return 'parallax-element'
      default:
        return ''
    }
  }

  return (
    <div
      ref={(node) => {
        ref(node)
        elementRef.current = node
      }}
      className={`scroll-reveal ${getVariantClasses()} ${className}`}
      style={{
        opacity: prefersReducedMotion ? 1 : (isVisible ? 1 : 0),
        transform: getTransform(isVisible),
        filter: getFilter(isVisible),
        transition: prefersReducedMotion
          ? 'none'
          : `all ${duration}s cubic-bezier(0.16, 1, 0.3, 1)`,
        transitionDelay: `${delay}s`,
        willChange: enableParallax || variant !== 'default'
          ? 'transform, opacity, filter'
          : 'transform, opacity, filter',
        transformStyle: variant === 'text-reveal' || variant === 'depth-lift'
          ? 'preserve-3d'
          : 'flat',
      }}
    >
      {children}
    </div>
  )
}
