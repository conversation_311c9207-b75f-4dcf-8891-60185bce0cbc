'use client'

import { Card, CardContent } from "@/components/ui/card"
import { motion } from 'framer-motion'
import { useIsTouchDevice } from '@/hooks/use-mobile'

interface SkillCardProps {
  title: string
  description: string
  imageSrc: string
}

export default function SkillCard({ title, description, imageSrc }: SkillCardProps) {
  const isTouchDevice = useIsTouchDevice()

  return (
    <motion.div
      whileHover={!isTouchDevice ? { scale: 1.05 } : {}}
      whileTap={{ scale: 0.95 }}
      className="p-4 xs:p-3 sm:p-4 md:p-5 lg:p-6 shadow-modern-lg hover:shadow-modern-xl rounded-xl bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 transition-all duration-300 touch-target h-full flex flex-col"
    >
      <div className="relative mb-3 xs:mb-2 sm:mb-3 md:mb-4 overflow-hidden rounded-md">
        <img
          src={imageSrc || "/placeholder.svg"}
          alt={title}
          className="w-full h-32 xs:h-28 sm:h-32 md:h-40 lg:h-48 object-cover transition-transform duration-300 group-hover:scale-105"
        />
      </div>
      <div className="flex-grow">
        <h3 className="text-base xs:text-sm sm:text-base md:text-lg lg:text-xl font-bold text-gray-900 dark:text-white mb-2 xs:mb-1 sm:mb-2 line-clamp-2">{title}</h3>
        <p className="text-xs xs:text-xs sm:text-sm md:text-sm text-gray-600 dark:text-gray-300 line-clamp-3 leading-relaxed">{description}</p>
      </div>
    </motion.div>
  )
}
