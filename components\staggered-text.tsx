'use client'

import React, { useRef, useEffect, useState } from 'react'
import { useInView } from 'react-intersection-observer'

interface StaggeredTextProps {
  children: React.ReactNode
  delay?: number
  staggerDelay?: number
  className?: string
  variant?: 'words' | 'lines' | 'characters'
  animation?: 'fade-up' | 'fade-in' | 'text-reveal' | 'scale-in'
}

export function StaggeredText({
  children,
  delay = 0,
  staggerDelay = 0.1,
  className = '',
  variant = 'words',
  animation = 'fade-up'
}: StaggeredTextProps) {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
    rootMargin: '-50px 0px',
  })
  
  const [isVisible, setIsVisible] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined'
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches
    : false

  useEffect(() => {
    if (inView && !prefersReducedMotion) {
      const timer = setTimeout(() => {
        setIsVisible(true)
      }, delay * 1000)
      return () => clearTimeout(timer)
    } else if (prefersReducedMotion) {
      setIsVisible(true)
    }
  }, [inView, delay, prefersReducedMotion])

  // Split text based on variant
  const splitText = (text: string) => {
    switch (variant) {
      case 'characters':
        return text.split('')
      case 'words':
        return text.split(' ')
      case 'lines':
        return text.split('\n')
      default:
        return [text]
    }
  }

  // Get animation styles
  const getAnimationStyle = (index: number) => {
    const animationDelay = delay + (index * staggerDelay)
    
    if (prefersReducedMotion) {
      return {
        opacity: 1,
        transform: 'none',
        transition: 'none'
      }
    }

    const baseStyle = {
      opacity: isVisible ? 1 : 0,
      transitionDelay: `${animationDelay}s`,
      transition: 'all 0.8s cubic-bezier(0.23, 1, 0.32, 1)',
      willChange: 'transform, opacity'
    }

    switch (animation) {
      case 'fade-up':
        return {
          ...baseStyle,
          transform: isVisible ? 'translateY(0)' : 'translateY(20px)'
        }
      case 'fade-in':
        return {
          ...baseStyle,
          transform: 'none'
        }
      case 'text-reveal':
        return {
          ...baseStyle,
          transform: isVisible 
            ? 'translateY(0) rotateX(0deg)' 
            : 'translateY(30px) rotateX(90deg)',
          transformOrigin: '50% 100%',
          transformStyle: 'preserve-3d' as const
        }
      case 'scale-in':
        return {
          ...baseStyle,
          transform: isVisible ? 'scale(1)' : 'scale(0.8)'
        }
      default:
        return baseStyle
    }
  }

  // Render text content
  const renderContent = () => {
    if (typeof children !== 'string') {
      return children
    }

    const parts = splitText(children)
    
    return parts.map((part, index) => (
      <span
        key={index}
        style={getAnimationStyle(index)}
        className={variant === 'words' ? 'inline-block mr-1' : 'inline-block'}
      >
        {part}
        {variant === 'words' && index < parts.length - 1 ? ' ' : ''}
      </span>
    ))
  }

  return (
    <div
      ref={ref}
      className={`staggered-text ${className}`}
    >
      <div
        ref={containerRef}
        className={`
          ${variant === 'lines' ? 'space-y-2' : ''}
          ${animation === 'text-reveal' ? 'transform-3d' : ''}
        `}
      >
        {renderContent()}
      </div>
    </div>
  )
}
