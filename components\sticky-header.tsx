'use client'

import React, { useState, useEffect } from 'react'
import { Moon, Sun, Menu, X, Code2 } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useIsMobile, useIsTouchDevice } from '@/hooks/use-mobile'

const navItems = [
  { name: 'Home', href: '#home' },
  { name: 'Services', href: '#services' },
  { name: 'Stats', href: '#stats' },
  { name: 'Projects', href: '#projects' },
  { name: 'Testimonials', href: '#testimonials' },
  { name: 'Contact', href: '#contact' },
]

export default function StickyHeader() {
  const [activeSection, setActiveSection] = useState('home')
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const { theme, setTheme } = useTheme()
  const isMobile = useIsMobile()
  const isTouchDevice = useIsTouchDevice()

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      const sections = navItems.map(item => item.href.substring(1))
      const scrollPosition = window.scrollY + 100

      for (const section of sections) {
        const element = document.getElementById(section)
        if (element) {
          const offsetTop = element.offsetTop
          const offsetHeight = element.offsetHeight

          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(section)
            break
          }
        }
      }
    }

    // Use native scroll
    window.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const scrollToSection = (href: string) => {
    const targetId = href.substring(1)
    const element = document.getElementById(targetId)

    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    setIsMenuOpen(false)
  }

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  if (!mounted) {
    return null
  }

  return (
    <header className="fixed top-0 left-0 w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200/20 dark:border-gray-700/20 shadow-modern-sm z-50 transition-all duration-300 safe-area-inset">
      <nav className="container mx-auto px-4 xs:px-2 sm:px-4 md:px-6 py-3 xs:py-2 sm:py-3 md:py-4 flex justify-between items-center">
        {/* Logo */}
        <div className="flex items-center space-x-2">
          <span className="text-lg xs:text-base sm:text-lg md:text-xl font-bold text-gray-900 dark:text-white">
            DevPortfolio
          </span>
        </div>

        {/* Desktop Navigation */}
        <ul className="hidden md:flex space-x-4 lg:space-x-6 xl:space-x-8">
          {navItems.map((item) => (
            <li key={item.name}>
              <button
                onClick={() => scrollToSection(item.href)}
                className={`relative text-sm lg:text-base font-medium transition-all duration-300 touch-target px-2 py-1 rounded-md ${
                  !isTouchDevice ? 'hover:text-pink-500 dark:hover:text-pink-400 hover:bg-pink-50 dark:hover:bg-pink-900/20' : ''
                } ${
                  activeSection === item.href.substring(1)
                    ? 'text-pink-500 dark:text-pink-400 bg-pink-50 dark:bg-pink-900/20'
                    : 'text-gray-700 dark:text-gray-300'
                }`}
                aria-label={`Navigate to ${item.name} section`}
              >
                {item.name}
                {activeSection === item.href.substring(1) && (
                  <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-primary rounded-full" />
                )}
              </button>
            </li>
          ))}
        </ul>

        {/* Theme Toggle & Mobile Menu */}
        <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4">
          {/* Theme Toggle */}
          <button
            onClick={toggleTheme}
            className={`touch-target p-2 sm:p-2.5 rounded-lg bg-gray-100 dark:bg-gray-800 transition-colors duration-300 min-w-[44px] min-h-[44px] flex items-center justify-center ${
              !isTouchDevice ? 'hover:bg-gray-200 dark:hover:bg-gray-700' : ''
            }`}
            aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`}
          >
            {theme === 'dark' ? (
              <Sun className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-500" />
            ) : (
              <Moon className="h-5 w-5 sm:h-6 sm:w-6 text-gray-700 dark:text-gray-300" />
            )}
          </button>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className={`md:hidden touch-target p-2 sm:p-2.5 rounded-lg bg-gray-100 dark:bg-gray-800 transition-colors duration-300 min-w-[44px] min-h-[44px] flex items-center justify-center ${
              !isTouchDevice ? 'hover:bg-gray-200 dark:hover:bg-gray-700' : ''
            }`}
            aria-label="Toggle mobile menu"
            aria-expanded={isMenuOpen}
          >
            {isMenuOpen ? (
              <X className="h-5 w-5 sm:h-6 sm:w-6 text-gray-700 dark:text-gray-300" />
            ) : (
              <Menu className="h-5 w-5 sm:h-6 sm:w-6 text-gray-700 dark:text-gray-300" />
            )}
          </button>
        </div>
      </nav>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-t border-gray-200/20 dark:border-gray-700/20 safe-area-inset">
          <ul className="px-4 xs:px-2 sm:px-4 py-4 xs:py-3 sm:py-4 md:py-6 space-y-2 xs:space-y-1 sm:space-y-2 mobile-touch-spacing">
            {navItems.map((item) => (
              <li key={item.name}>
                <button
                  onClick={() => scrollToSection(item.href)}
                  className={`block w-full text-left text-base xs:text-sm sm:text-base lg:text-lg font-medium transition-colors duration-300 touch-target py-3 px-4 rounded-lg ${
                    activeSection === item.href.substring(1)
                      ? 'text-pink-500 dark:text-pink-400 bg-pink-50 dark:bg-pink-900/20'
                      : 'text-gray-700 dark:text-gray-300'
                  } ${
                    !isTouchDevice ? 'hover:bg-gray-50 dark:hover:bg-gray-800/50' : ''
                  }`}
                  aria-label={`Navigate to ${item.name} section`}
                >
                  {item.name}
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </header>
  )
}