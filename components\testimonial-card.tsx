import { Card, CardContent } from "@/components/ui/card"

interface TestimonialCardProps {
  quote: string
  author: string
  role: string
  avatarSrc: string
}

export default function TestimonialCard({ quote, author, role, avatarSrc }: TestimonialCardProps) {
  return (
    <Card className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-colors duration-300 h-full touch-target">
      <CardContent className="p-4 xs:p-3 sm:p-4 md:p-5 lg:p-6 flex flex-col h-full">
        <div className="mb-3 xs:mb-2 sm:mb-3 md:mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 xs:h-5 xs:w-5 sm:h-6 sm:w-6 md:h-7 md:w-7 lg:h-8 lg:w-8 text-pink-500 opacity-80"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
          </svg>
        </div>
        <p className="text-white/90 mb-4 xs:mb-3 sm:mb-4 md:mb-5 lg:mb-6 flex-grow text-sm xs:text-xs sm:text-sm md:text-base leading-relaxed line-clamp-4">{quote}</p>
        <div className="flex items-center mt-auto">
          <div className="w-8 h-8 xs:w-7 xs:h-7 sm:w-8 sm:h-8 md:w-9 md:h-9 lg:w-10 lg:h-10 rounded-full overflow-hidden mr-3 xs:mr-2 sm:mr-3 flex-shrink-0">
            <img src={avatarSrc || "/placeholder.svg"} alt={author} className="w-full h-full object-cover" />
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="font-semibold text-white text-sm xs:text-xs sm:text-sm md:text-base truncate">{author}</h4>
            <p className="text-white/70 text-xs xs:text-xs sm:text-sm truncate">{role}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
