'use client'

import * as React from 'react'
import {
  ThemeProvider as NextThemesProvider,
  type ThemeProviderProps,
} from 'next-themes'

const designSystem = {
  colors: {
    primary: '#4F46E5',
    secondary: '#9333EA',
    background: '#F9FAFB',
    foreground: '#111827',
    muted: '#6B7280',
  },
  typography: {
    fontFamily: 'Inter, sans-serif',
    heading: '2rem',
    body: '1rem',
  },
}

export const DesignSystemContext = React.createContext(designSystem)

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider {...props}>
      <DesignSystemContext.Provider value={designSystem}>
        {children}
      </DesignSystemContext.Provider>
    </NextThemesProvider>
  )
}
