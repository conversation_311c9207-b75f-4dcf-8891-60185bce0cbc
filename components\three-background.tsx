"use client"

import { useEffect, useRef, useState } from "react"
import * as THREE from "three"

export default function ThreeBackground() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [scrollY, setScrollY] = useState(0)

  useEffect(() => {
    if (!containerRef.current) return

    // Dynamically import Three.js modules
    const loadThreeModules = async () => {
      try {
        // Scene setup
        const scene = new THREE.Scene()
        scene.background = new THREE.Color("#0f0a1e") // Dark purple background

        // Camera setup
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000)
        camera.position.z = 5

        // Renderer setup
        const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
        renderer.setSize(window.innerWidth, window.innerHeight)
        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
        containerRef.current.appendChild(renderer.domElement)

        // Lighting
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
        scene.add(ambientLight)

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
        directionalLight.position.set(5, 5, 5)
        scene.add(directionalLight)

        const pointLight1 = new THREE.PointLight(0xff00ff, 2, 10) // Pink light
        pointLight1.position.set(2, 2, 2)
        scene.add(pointLight1)

        const pointLight2 = new THREE.PointLight(0x00ffff, 2, 10) // Cyan light
        pointLight2.position.set(-2, -2, 2)
        scene.add(pointLight2)

        // Particles
        const particlesGeometry = new THREE.BufferGeometry()
        const particlesCount = 2000

        const posArray = new Float32Array(particlesCount * 3)
        for (let i = 0; i < particlesCount * 3; i++) {
          posArray[i] = (Math.random() - 0.5) * 15
        }

        particlesGeometry.setAttribute("position", new THREE.BufferAttribute(posArray, 3))

        const particlesMaterial = new THREE.PointsMaterial({
          size: 0.02,
          color: 0xffffff,
          transparent: true,
          opacity: 0.8,
          blending: THREE.AdditiveBlending,
        })

        const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial)
        scene.add(particlesMesh)

        // Create a simple planet instead of loading a model
        const planetGeometry = new THREE.SphereGeometry(1, 32, 32)
        const planetMaterial = new THREE.MeshStandardMaterial({
          color: 0x8844aa,
          roughness: 0.7,
          metalness: 0.2,
        })
        const planet = new THREE.Mesh(planetGeometry, planetMaterial)
        scene.add(planet)

        // Create rings around the planet
        const ringGeometry = new THREE.RingGeometry(1.5, 2, 32)
        const ringMaterial = new THREE.MeshBasicMaterial({
          color: 0xff88cc,
          side: THREE.DoubleSide,
          transparent: true,
          opacity: 0.5,
        })
        const ring = new THREE.Mesh(ringGeometry, ringMaterial)
        ring.rotation.x = Math.PI / 2
        scene.add(ring)

        // Handle window resize
        const handleResize = () => {
          camera.aspect = window.innerWidth / window.innerHeight
          camera.updateProjectionMatrix()
          renderer.setSize(window.innerWidth, window.innerHeight)
        }

        window.addEventListener("resize", handleResize)

        // Handle scroll
        const handleScroll = () => {
          setScrollY(window.scrollY)
        }

        window.addEventListener("scroll", handleScroll)

        // Animation loop
        const animate = () => {
          requestAnimationFrame(animate)

          // Rotate particles
          particlesMesh.rotation.x += 0.0003
          particlesMesh.rotation.y += 0.0005

          // Move particles based on scroll
          const scrollFactor = scrollY * 0.0005
          particlesMesh.position.y = -scrollFactor

          // Rotate planet
          planet.rotation.y += 0.005
          planet.position.y = Math.sin(Date.now() * 0.001) * 0.2 - scrollFactor

          // Rotate ring
          ring.rotation.z += 0.002
          ring.position.y = planet.position.y

          // Move lights
          const time = Date.now() * 0.001
          pointLight1.position.x = Math.sin(time) * 3
          pointLight1.position.z = Math.cos(time) * 3
          pointLight2.position.x = Math.sin(time + Math.PI) * 3
          pointLight2.position.z = Math.cos(time + Math.PI) * 3

          renderer.render(scene, camera)
        }

        animate()

        // Cleanup
        return () => {
          window.removeEventListener("resize", handleResize)
          window.removeEventListener("scroll", handleScroll)
          if (containerRef.current && containerRef.current.contains(renderer.domElement)) {
            containerRef.current.removeChild(renderer.domElement)
          }

          // Dispose resources
          particlesGeometry.dispose()
          particlesMaterial.dispose()
          planetGeometry.dispose()
          planetMaterial.dispose()
          ringGeometry.dispose()
          ringMaterial.dispose()
          renderer.dispose()
        }
      } catch (error) {
        console.error("Error initializing Three.js:", error)
      }
    }

    loadThreeModules()
  }, [scrollY])

  return (
    <div
      ref={containerRef}
      className="fixed top-0 left-0 w-full h-full z-0 pointer-events-none"
      style={{ opacity: 0.7 }}
    />
  )
}
